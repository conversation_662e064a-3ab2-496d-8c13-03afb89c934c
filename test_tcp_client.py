#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TCP客户端，验证预创建浏览器功能
"""

import socket
import json
import time

def send_tcp_command(command, config_variant=None, host='127.0.0.1', port=8026):
    """发送TCP命令"""
    if config_variant is None:
        config_variant = {}
    
    message = {
        "command": command,
        "ConfigVariant": config_variant
    }
    
    try:
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect((host, port))
        
        # 发送消息
        message_str = json.dumps(message, ensure_ascii=False)
        sock.sendall(message_str.encode('utf-8'))
        
        # 接收响应
        response = sock.recv(4096).decode('utf-8')
        sock.close()
        
        print(f"发送命令: {command}")
        print(f"响应: {response}")
        print("-" * 50)
        
        return response
        
    except Exception as e:
        print(f"发送命令失败: {str(e)}")
        return None

def test_browser_commands():
    """测试浏览器命令"""
    print("=== 测试TCP浏览器命令 ===")
    
    # 测试命令列表
    commands = [
        ("wifi_page", {"keep_open": "3"}),
        ("system_page", {"keep_open": "3"}),
        ("preview_page", {"keep_open": "3"}),
        ("wifi_page", {"keep_open": "2"}),  # 重复测试
        ("close_browser", {}),
    ]
    
    for i, (cmd, config) in enumerate(commands, 1):
        print(f"\n{i}. 测试命令: {cmd}")
        start_time = time.time()
        
        response = send_tcp_command(cmd, config)
        
        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        
        if response:
            try:
                resp_data = json.loads(response)
                if resp_data.get("success"):
                    print("✅ 命令执行成功")
                else:
                    print("❌ 命令执行失败")
            except:
                print("⚠️ 响应格式异常")
        
        # 等待一下再发送下一个命令
        if i < len(commands):
            time.sleep(1)

def test_rapid_commands():
    """测试快速连续命令"""
    print("\n=== 测试快速连续命令 ===")
    
    commands = ["wifi_page", "system_page", "preview_page"]
    
    start_time = time.time()
    
    for cmd in commands:
        print(f"快速发送: {cmd}")
        send_tcp_command(cmd, {"keep_open": "1"})
    
    total_time = time.time() - start_time
    print(f"\n3个命令总耗时: {total_time:.2f}秒")
    print(f"平均每个命令: {total_time/3:.2f}秒")

def main():
    """主测试函数"""
    print("TCP客户端测试 - 验证预创建浏览器功能")
    print("=" * 60)
    
    try:
        # 等待服务器完全启动
        print("等待服务器启动完成...")
        time.sleep(2)
        
        # 基本命令测试
        test_browser_commands()
        
        # 快速命令测试
        test_rapid_commands()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("\n观察要点:")
        print("1. 第一个命令应该很快响应（因为浏览器已预创建）")
        print("2. 后续命令应该更快（复用浏览器）")
        print("3. 浏览器应该在指令期间显示，结束后隐藏")
        print("4. 整体响应速度应该比之前快很多")
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
