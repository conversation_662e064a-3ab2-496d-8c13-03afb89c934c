#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试预创建浏览器功能
验证程序启动时预创建浏览器，指令来了直接显示并跳转页面
"""

import time
from SyncWebTool import BrowserManager

def test_pre_create_and_show_hide():
    """测试预创建浏览器和显示/隐藏功能"""
    print("=== 测试预创建浏览器和显示/隐藏功能 ===")
    
    # 1. 预创建浏览器（应该隐藏）
    print("\n1. 预创建浏览器实例...")
    success = BrowserManager.pre_create_browser()
    print(f"预创建结果: {'成功' if success else '失败'}")
    
    if not success:
        print("预创建失败，退出测试")
        return False
    
    print("浏览器已预创建并隐藏，等待3秒...")
    time.sleep(3)
    
    # 2. 模拟第一个指令 - 打开wifi页面
    print("\n2. 模拟指令：打开wifi页面...")
    success1 = BrowserManager.navigate_to_page('wifi', keep_open=3)
    print(f"wifi页面结果: {'成功' if success1 else '失败'}")
    
    # 3. 模拟第二个指令 - 打开system页面
    print("\n3. 模拟指令：打开system页面...")
    success2 = BrowserManager.navigate_to_page('system', keep_open=3)
    print(f"system页面结果: {'成功' if success2 else '失败'}")
    
    # 4. 模拟第三个指令 - 打开preview页面
    print("\n4. 模拟指令：打开preview页面...")
    success3 = BrowserManager.navigate_to_page('preview', keep_open=3)
    print(f"preview页面结果: {'成功' if success3 else '失败'}")
    
    # 5. 验证浏览器是否隐藏
    print("\n5. 验证浏览器状态...")
    if BrowserManager._is_hidden:
        print("✅ 浏览器已正确隐藏")
    else:
        print("❌ 浏览器未隐藏")
    
    # 6. 最终关闭浏览器
    print("\n6. 关闭浏览器...")
    BrowserManager.close_browser()
    print("浏览器已关闭")
    
    print(f"\n=== 测试完成 ===")
    all_success = all([success, success1, success2, success3])
    print(f"总体结果: {'全部成功' if all_success else '有失败'}")
    
    return all_success

def test_performance_comparison():
    """测试性能对比"""
    print("\n=== 性能对比测试 ===")
    
    pages = ['wifi', 'system', 'preview']
    
    # 测试预创建版本的性能
    print("\n测试预创建+复用版本...")
    
    # 预创建浏览器
    start_time = time.time()
    BrowserManager.pre_create_browser()
    pre_create_time = time.time() - start_time
    print(f"预创建耗时: {pre_create_time:.2f}秒")
    
    # 测试页面打开速度
    page_times = []
    for page in pages:
        print(f"  打开{page}页面...")
        start_time = time.time()
        BrowserManager.navigate_to_page(page, keep_open=0)  # 不等待
        page_time = time.time() - start_time
        page_times.append(page_time)
        print(f"  {page}页面耗时: {page_time:.2f}秒")
    
    total_time = pre_create_time + sum(page_times)
    avg_page_time = sum(page_times) / len(page_times)
    
    print(f"\n性能统计:")
    print(f"预创建耗时: {pre_create_time:.2f}秒")
    print(f"平均页面打开耗时: {avg_page_time:.2f}秒")
    print(f"总耗时: {total_time:.2f}秒")
    
    # 关闭浏览器
    BrowserManager.close_browser()
    
    return total_time

def test_tcp_simulation():
    """模拟TCP服务器的使用场景"""
    print("\n=== 模拟TCP服务器场景 ===")
    
    # 1. 程序启动时预创建浏览器
    print("1. 程序启动 - 预创建浏览器...")
    BrowserManager.pre_create_browser()
    print("浏览器已预创建并隐藏")
    
    # 2. 模拟多个TCP指令
    commands = [
        ('wifi_page', 'wifi'),
        ('system_page', 'system'),
        ('preview_page', 'preview'),
        ('wifi_page', 'wifi'),  # 重复指令测试
    ]
    
    results = []
    for i, (cmd_name, page_name) in enumerate(commands, 1):
        print(f"\n{i}. 收到TCP指令: {cmd_name}")
        print(f"   处理页面: {page_name}")
        
        start_time = time.time()
        success = BrowserManager.navigate_to_page(page_name, keep_open=2)
        elapsed = time.time() - start_time
        
        results.append(success)
        print(f"   结果: {'成功' if success else '失败'} (耗时: {elapsed:.2f}秒)")
    
    # 3. 程序关闭时清理
    print(f"\n程序关闭 - 清理浏览器...")
    BrowserManager.close_browser()
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n指令成功率: {success_rate:.1f}%")
    
    return success_rate >= 75  # 75%以上成功率算通过

def main():
    """主测试函数"""
    print("预创建浏览器功能测试")
    print("=" * 50)
    
    test_results = []
    
    try:
        # 测试1: 基本功能
        print("测试1: 基本预创建和显示/隐藏功能")
        result1 = test_pre_create_and_show_hide()
        test_results.append(("基本功能", result1))
        
        time.sleep(2)  # 间隔
        
        # 测试2: 性能测试
        print("\n测试2: 性能测试")
        total_time = test_performance_comparison()
        result2 = total_time < 15  # 期望总时间小于15秒
        test_results.append(("性能测试", result2))
        
        time.sleep(2)  # 间隔
        
        # 测试3: TCP场景模拟
        print("\n测试3: TCP服务器场景模拟")
        result3 = test_tcp_simulation()
        test_results.append(("TCP场景", result3))
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 确保清理
        try:
            BrowserManager.close_browser()
        except:
            pass
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    print(f"\n通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！预创建浏览器功能工作正常。")
        print("\n主要优势:")
        print("- 🚀 程序启动时预创建浏览器，指令响应更快")
        print("- 👁️ 浏览器自动隐藏/显示，用户体验更好")
        print("- 🔄 完美的浏览器复用，资源利用率高")
        print("- ⚡ 指令处理速度大幅提升")
    else:
        print("\n❌ 部分测试失败，请检查实现或网络连接。")

if __name__ == "__main__":
    main()
