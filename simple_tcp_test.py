#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的TCP客户端测试
"""

import socket
import json
import time

def send_command(cmd, keep_open="3"):
    """发送TCP命令"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('127.0.0.1', 8026))
        
        message = {
            "command": cmd,
            "ConfigVariant": {"keep_open": keep_open}
        }
        
        message_str = json.dumps(message, ensure_ascii=False)
        print(f"发送: {message_str}")
        
        sock.sendall(message_str.encode('utf-8'))
        response = sock.recv(4096).decode('utf-8')
        
        print(f"响应: {response}")
        sock.close()
        
        return True
    except Exception as e:
        print(f"错误: {str(e)}")
        return False

def main():
    print("=== 简单TCP测试 ===")
    
    # 测试命令
    commands = ["wifi_page", "system_page", "preview_page"]
    
    for cmd in commands:
        print(f"\n测试命令: {cmd}")
        success = send_command(cmd, "2")
        print(f"结果: {'成功' if success else '失败'}")
        time.sleep(1)
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
