#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试BrowserManager功能
"""

import time
from SyncWebTool import B<PERSON>erMana<PERSON>

def test_direct_browser_manager():
    """直接测试BrowserManager"""
    print("=== 直接测试BrowserManager ===")
    
    try:
        # 1. 预创建浏览器
        print("\n1. 预创建浏览器...")
        success = BrowserManager.pre_create_browser()
        print(f"预创建结果: {'成功' if success else '失败'}")
        
        if not success:
            print("预创建失败，退出测试")
            return False
        
        # 2. 测试页面打开
        pages = ['wifi', 'system', 'preview']
        results = []
        
        for i, page in enumerate(pages, 1):
            print(f"\n{i}. 测试打开{page}页面...")
            start_time = time.time()
            
            success = BrowserManager.navigate_to_page(page, keep_open=2)
            
            elapsed = time.time() - start_time
            results.append(success)
            
            print(f"   结果: {'成功' if success else '失败'}")
            print(f"   耗时: {elapsed:.2f}秒")
        
        # 3. 清理
        print(f"\n清理浏览器...")
        BrowserManager.close_browser()
        
        # 总结
        success_count = sum(results)
        total_count = len(results)
        success_rate = success_count / total_count * 100
        
        print(f"\n=== 测试完成 ===")
        print(f"成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        if success_rate >= 75:
            print("✅ BrowserManager工作正常！")
            return True
        else:
            print("❌ BrowserManager存在问题")
            return False
            
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("BrowserManager直接测试")
    print("=" * 40)
    
    success = test_direct_browser_manager()
    
    if success:
        print("\n🎉 BrowserManager测试通过！")
        print("现在可以确认TCP服务器应该也能正常工作。")
    else:
        print("\n❌ BrowserManager测试失败，需要检查实现。")

if __name__ == "__main__":
    main()
