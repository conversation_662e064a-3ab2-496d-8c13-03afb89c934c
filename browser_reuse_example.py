#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器复用功能使用示例
演示如何使用新的浏览器复用功能来提高页面打开速度
"""

import time
from SyncWebTool import BrowserManager, open_device_page, open_device_page_async

def example_1_direct_use():
    """示例1: 直接使用现有函数（推荐方式）"""
    print("=== 示例1: 直接使用现有函数 ===")
    
    # 这些函数现在会自动使用浏览器复用
    print("1. 打开wifi页面...")
    success1 = open_device_page('wifi', keep_open=2)
    print(f"结果: {'成功' if success1 else '失败'}")
    
    print("2. 打开系统信息页面...")
    success2 = open_device_page('system', keep_open=2)
    print(f"结果: {'成功' if success2 else '失败'}")
    
    print("3. 打开预览页面...")
    success3 = open_device_page('preview', keep_open=2)
    print(f"结果: {'成功' if success3 else '失败'}")
    
    return all([success1, success2, success3])

def example_2_browser_manager():
    """示例2: 直接使用BrowserManager"""
    print("\n=== 示例2: 直接使用BrowserManager ===")
    
    try:
        # 打开多个页面，观察复用效果
        pages = ['wifi', 'system', 'network', 'preview']
        results = []
        
        for i, page in enumerate(pages, 1):
            print(f"{i}. 打开{page}页面...")
            success = BrowserManager.navigate_to_page(page, keep_open=1)
            results.append(success)
            print(f"结果: {'成功' if success else '失败'}")
        
        return all(results)
    
    finally:
        # 手动关闭浏览器
        print("关闭浏览器...")
        BrowserManager.close_browser()

def example_3_async_version():
    """示例3: 使用异步版本"""
    print("\n=== 示例3: 使用异步版本 ===")
    
    # 异步版本，浏览器会保持打开以供复用
    print("1. 异步打开wifi页面...")
    success1 = open_device_page_async('wifi', keep_open=2, auto_close=False)
    
    print("2. 异步打开系统页面...")
    success2 = open_device_page_async('system', keep_open=2, auto_close=False)
    
    print("3. 异步打开预览页面...")
    success3 = open_device_page_async('preview', keep_open=2, auto_close=True)  # 最后一个关闭
    
    return all([success1, success2, success3])

def example_4_performance_test():
    """示例4: 性能测试"""
    print("\n=== 示例4: 性能测试 ===")
    
    pages = ['wifi', 'system', 'network', 'preview']
    
    # 测试复用版本的性能
    print("测试浏览器复用版本...")
    start_time = time.time()
    
    for page in pages:
        print(f"  打开{page}页面...")
        BrowserManager.navigate_to_page(page, keep_open=0)
    
    reuse_time = time.time() - start_time
    print(f"复用版本总耗时: {reuse_time:.2f}秒")
    
    # 关闭浏览器
    BrowserManager.close_browser()
    
    return reuse_time

def example_5_error_recovery():
    """示例5: 错误恢复测试"""
    print("\n=== 示例5: 错误恢复测试 ===")
    
    # 打开一个页面
    print("1. 正常打开页面...")
    success1 = BrowserManager.navigate_to_page('system', keep_open=1)
    print(f"结果: {'成功' if success1 else '失败'}")
    
    # 手动关闭浏览器模拟意外关闭
    print("2. 模拟浏览器意外关闭...")
    BrowserManager.close_browser()
    
    # 再次打开页面，应该自动重新创建浏览器
    print("3. 重新打开页面（应该自动重新创建浏览器）...")
    success2 = BrowserManager.navigate_to_page('wifi', keep_open=1)
    print(f"结果: {'成功' if success2 else '失败'}")
    
    BrowserManager.close_browser()
    return success1 and success2

def main():
    """主函数"""
    print("浏览器复用功能使用示例")
    print("=" * 50)
    
    results = []
    
    # 运行所有示例
    try:
        results.append(example_1_direct_use())
        results.append(example_2_browser_manager())
        results.append(example_3_async_version())
        
        # 性能测试
        reuse_time = example_4_performance_test()
        results.append(reuse_time < 10)  # 期望总时间小于10秒
        
        results.append(example_5_error_recovery())
        
    except Exception as e:
        print(f"示例运行出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 确保最后关闭浏览器
        try:
            BrowserManager.close_browser()
        except:
            pass
    
    # 总结
    print("\n" + "=" * 50)
    print("示例运行总结:")
    print(f"成功的示例: {sum(results)}/{len(results)}")
    
    if all(results):
        print("✅ 所有示例都运行成功！浏览器复用功能工作正常。")
    else:
        print("❌ 部分示例失败，请检查网络连接和设备状态。")
    
    print("\n主要优势:")
    print("- 🚀 大幅提升页面打开速度")
    print("- 💾 减少内存和CPU使用")
    print("- 🔄 自动处理浏览器生命周期")
    print("- 🛡️ 自动错误恢复")
    print("- 🔧 完全兼容现有代码")

if __name__ == "__main__":
    main()
