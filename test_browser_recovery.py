#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试浏览器恢复功能
验证浏览器被人为关闭后能自动重新创建，以及窗口置顶功能
"""

import time
from SyncWebTool import BrowserManager

def test_browser_recovery():
    """测试浏览器被关闭后的恢复功能"""
    print("=== 测试浏览器恢复功能 ===")
    
    # 1. 预创建浏览器
    print("\n1. 预创建浏览器...")
    success = BrowserManager.pre_create_browser()
    print(f"预创建结果: {'成功' if success else '失败'}")
    
    if not success:
        print("预创建失败，退出测试")
        return False
    
    # 2. 打开第一个页面
    print("\n2. 打开wifi页面...")
    success1 = BrowserManager.navigate_to_page('wifi', keep_open=3)
    print(f"wifi页面结果: {'成功' if success1 else '失败'}")
    
    # 3. 提示用户手动关闭浏览器
    print("\n3. 请手动关闭浏览器窗口（点击X按钮）...")
    print("   等待15秒让用户操作...")
    
    for i in range(15, 0, -1):
        print(f"   倒计时: {i}秒", end='\r')
        time.sleep(1)
    print("\n   时间到！")
    
    # 4. 尝试打开第二个页面（应该自动重新创建浏览器）
    print("\n4. 尝试打开system页面（测试自动恢复）...")
    success2 = BrowserManager.navigate_to_page('system', keep_open=3)
    print(f"system页面结果: {'成功' if success2 else '失败'}")
    
    if success2:
        print("✅ 浏览器自动恢复成功！")
    else:
        print("❌ 浏览器自动恢复失败")
    
    # 5. 测试连续操作
    print("\n5. 测试连续页面操作...")
    pages = ['wifi', 'preview']
    results = []
    
    for page in pages:
        print(f"   打开{page}页面...")
        success = BrowserManager.navigate_to_page(page, keep_open=2)
        results.append(success)
        print(f"   结果: {'成功' if success else '失败'}")
    
    # 6. 清理
    print("\n6. 清理浏览器...")
    BrowserManager.close_browser()
    
    # 总结
    all_success = success1 and success2 and all(results)
    print(f"\n=== 测试完成 ===")
    print(f"总体结果: {'全部成功' if all_success else '有失败'}")
    
    return all_success

def test_window_focus():
    """测试窗口置顶功能"""
    print("\n=== 测试窗口置顶功能 ===")
    
    # 1. 预创建并隐藏浏览器
    print("\n1. 预创建浏览器...")
    BrowserManager.pre_create_browser()
    
    # 2. 提示用户打开其他应用
    print("\n2. 请打开其他应用程序（如记事本、文件管理器等）...")
    print("   让其他窗口处于前台，然后等待...")
    
    for i in range(10, 0, -1):
        print(f"   倒计时: {i}秒", end='\r')
        time.sleep(1)
    print("\n   时间到！")
    
    # 3. 打开页面，测试是否能置顶
    print("\n3. 打开页面，测试浏览器是否能置顶...")
    success = BrowserManager.navigate_to_page('wifi', keep_open=5)
    
    if success:
        print("✅ 页面打开成功！请检查浏览器是否已置顶到前台")
        print("   浏览器应该显示在所有其他窗口的前面")
    else:
        print("❌ 页面打开失败")
    
    # 4. 清理
    BrowserManager.close_browser()
    
    return success

def test_multiple_recovery_cycles():
    """测试多次恢复循环"""
    print("\n=== 测试多次恢复循环 ===")
    
    results = []
    
    for cycle in range(1, 4):  # 测试3个循环
        print(f"\n--- 循环 {cycle} ---")
        
        # 预创建
        print(f"循环{cycle}: 预创建浏览器...")
        BrowserManager.pre_create_browser()
        
        # 打开页面
        print(f"循环{cycle}: 打开页面...")
        success = BrowserManager.navigate_to_page('wifi', keep_open=1)
        results.append(success)
        
        # 强制关闭浏览器（模拟用户关闭）
        print(f"循环{cycle}: 强制关闭浏览器...")
        BrowserManager.close_browser()
        
        print(f"循环{cycle}: 结果 {'成功' if success else '失败'}")
    
    success_rate = sum(results) / len(results) * 100
    print(f"\n多次恢复测试完成，成功率: {success_rate:.1f}%")
    
    return success_rate >= 80  # 80%以上成功率算通过

def main():
    """主测试函数"""
    print("浏览器恢复和置顶功能测试")
    print("=" * 50)
    
    test_results = []
    
    try:
        # 测试1: 基本恢复功能
        print("测试1: 浏览器恢复功能")
        result1 = test_browser_recovery()
        test_results.append(("恢复功能", result1))
        
        time.sleep(2)  # 间隔
        
        # 测试2: 窗口置顶功能
        print("\n测试2: 窗口置顶功能")
        result2 = test_window_focus()
        test_results.append(("置顶功能", result2))
        
        time.sleep(2)  # 间隔
        
        # 测试3: 多次恢复循环
        print("\n测试3: 多次恢复循环")
        result3 = test_multiple_recovery_cycles()
        test_results.append(("多次恢复", result3))
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 确保清理
        try:
            BrowserManager.close_browser()
        except:
            pass
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    print(f"\n通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！")
        print("\n修复效果:")
        print("- ✅ 浏览器被关闭后能自动重新创建")
        print("- ✅ 浏览器窗口能正确置顶到前台")
        print("- ✅ 错误恢复机制工作正常")
        print("- ✅ 多次恢复循环稳定可靠")
    else:
        print("\n❌ 部分测试失败，请检查实现。")
    
    print("\n使用说明:")
    print("1. 当浏览器被人为关闭时，系统会自动检测并重新创建")
    print("2. 新的浏览器窗口会自动置顶到前台")
    print("3. 所有操作对用户透明，无需手动干预")

if __name__ == "__main__":
    main()
