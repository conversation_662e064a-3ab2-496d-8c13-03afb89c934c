# -*- coding: utf-8 -*-

# 浏览器复用管理器
class BrowserManager:
    """浏览器复用管理器 - 简单有效的实现"""

    _instance = None
    _browser_tool = None
    _last_device_ip = None
    _last_password = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BrowserManager, cls).__new__(cls)
        return cls._instance

    @classmethod
    def get_browser(cls, device_ip="**************", password="111111", headless=False):
        """获取浏览器实例，如果不存在或参数变化则创建新的"""
        instance = cls()

        # 检查是否需要创建新的浏览器实例
        need_new_browser = (
            cls._browser_tool is None or
            cls._last_device_ip != device_ip or
            cls._last_password != password or
            not cls._is_browser_alive()
        )

        if need_new_browser:
            # 关闭旧的浏览器
            cls.close_browser()

            # 创建新的浏览器实例
            print(f"[浏览器管理器] 创建新的浏览器实例 - IP: {device_ip}")
            cls._browser_tool = SyncWebTool(device_ip, password, headless)

            if cls._browser_tool.start():
                cls._last_device_ip = device_ip
                cls._last_password = password
                print(f"[浏览器管理器] 浏览器启动成功")
            else:
                print(f"[浏览器管理器] 浏览器启动失败")
                cls._browser_tool = None
                return None
        else:
            print(f"[浏览器管理器] 复用现有浏览器实例")

        return cls._browser_tool

    @classmethod
    def _is_browser_alive(cls):
        """检查浏览器是否还活着"""
        if cls._browser_tool is None:
            return False
        try:
            # 简单检查浏览器是否还能响应
            if cls._browser_tool.browser and cls._browser_tool.page:
                # 尝试获取当前URL，如果失败说明浏览器已关闭
                cls._browser_tool.page.url
                return True
        except:
            pass
        return False

    @classmethod
    def close_browser(cls):
        """关闭浏览器"""
        if cls._browser_tool:
            try:
                print(f"[浏览器管理器] 关闭浏览器")
                cls._browser_tool.close()
            except:
                pass
            cls._browser_tool = None
            cls._last_device_ip = None
            cls._last_password = None

    @classmethod
    def navigate_to_page(cls, page_name, device_ip="**************", password="111111",
                        screenshot=False, keep_open=0):
        """导航到指定页面"""
        try:
            # 获取浏览器实例
            tool = cls.get_browser(device_ip, password, headless=False)
            if not tool:
                return False

            # 页面映射
            if page_name in PAGE_MAP:
                full_page_name = PAGE_MAP[page_name]
            else:
                full_page_name = page_name

            print(f"[浏览器管理器] 导航到页面: {page_name} -> {full_page_name}")

            # 使用增强的登录方法
            success = enhanced_login_and_goto(tool, full_page_name)

            if success:
                print(f"[浏览器管理器] 成功打开页面: {page_name}")
                if screenshot:
                    try:
                        tool.take_screenshot(f"{page_name}_page.png")
                        print(f"[浏览器管理器] 截图已保存: {page_name}_page.png")
                    except Exception as e:
                        print(f"[浏览器管理器] 截图失败: {str(e)}")

                if keep_open > 0:
                    print(f"[浏览器管理器] 页面将保持显示 {keep_open} 秒...")
                    import time
                    time.sleep(keep_open)
            else:
                print(f"[浏览器管理器] 页面打开失败: {page_name}")

            return success

        except Exception as e:
            print(f"[浏览器管理器] 导航失败: {str(e)}")
            import traceback
            print(f"[浏览器管理器] 详细错误: {traceback.format_exc()}")
            return False


# def open_device_page(page_name, device_ip="**************", password="111111",
#                     screenshot=True, keep_open=10):
#     """Simple function to open device page"""
#     tool = SyncWebTool(device_ip, password, headless=False)
#
#     try:
#         if not tool.start():
#             return False
#
#         if page_name in PAGE_MAP:
#             full_page_name = PAGE_MAP[page_name]
#         else:
#             full_page_name = page_name
#
#         success = tool.login_and_goto(full_page_name)
#
#         if success:
#             if screenshot:
#                 tool.take_screenshot(f"{page_name}_page.png")
#
#             if keep_open > 0:
#                 print(f"Browser will stay open for {keep_open} seconds...")
#                 time.sleep(keep_open)
#
#         return success
#
#     finally:
#         tool.close()


# def open_device_page_async(page_name, device_ip="**************", password="111111",
#                           screenshot=False, keep_open=10, auto_close=True):
#     """异步友好的设备页面打开函数 - 增强版"""
#     print(f"[异步浏览器] 开始处理页面: {page_name}, IP: {device_ip}")
#
#     tool = SyncWebTool(device_ip, password, headless=False)
#
#     try:
#         print(f"[异步浏览器] 启动浏览器...")
#         if not tool.start():
#             error_msg = f"Failed to start browser for {page_name}"
#             print(f"[异步浏览器] {error_msg}")
#             return False
#
#         print(f"[异步浏览器] 浏览器启动成功，准备打开页面...")
#
#         if page_name in PAGE_MAP:
#             full_page_name = PAGE_MAP[page_name]
#             print(f"[异步浏览器] 页面映射: {page_name} -> {full_page_name}")
#         else:
#             full_page_name = page_name
#             print(f"[异步浏览器] 直接使用页面名: {full_page_name}")
#
#         # 使用增强的登录方法
#         print(f"[异步浏览器] 开始增强登录流程...")
#         success = enhanced_login_and_goto(tool, full_page_name)
#
#         if success:
#             print(f"[异步浏览器] 成功打开 {page_name} 页面")
#             if screenshot:
#                 try:
#                     tool.take_screenshot(f"{page_name}_page.png")
#                     print(f"[异步浏览器] 截图已保存: {page_name}_page.png")
#                 except Exception as e:
#                     print(f"[异步浏览器] 截图失败: {str(e)}")
#
#             if keep_open > 0:
#                 print(f"[异步浏览器] 浏览器将保持打开 {keep_open} 秒...")
#                 if not auto_close:
#                     print(f"[异步浏览器] 注意: 浏览器不会自动关闭，需要手动关闭")
#                 time.sleep(keep_open)
#         else:
#             print(f"[异步浏览器] 登录或页面跳转失败: {page_name}")
#
#         return success
#
#     except Exception as e:
#         error_msg = f"Error opening {page_name} page: {str(e)}"
#         print(f"[异步浏览器] {error_msg}")
#         import traceback
#         print(f"[异步浏览器] 详细错误信息: {traceback.format_exc()}")
#         return False
#     finally:
#         if auto_close:
#             print(f"[异步浏览器] 关闭浏览器: {page_name}")
#             tool.close()
#         else:
#             print(f"[异步浏览器] 浏览器保持打开: {page_name} (需要手动关闭)")


def enhanced_login_and_goto(tool, target_page):
    """增强的登录和页面跳转函数，专为异步环境优化"""
    try:
        print(f"[增强登录] 开始登录流程...")
        logging.info(f"Enhanced login and goto {target_page}")

        # 1. 先访问登录页面
        login_url = f"{tool.base_url}/login.html"
        print(f"[增强登录] 访问登录页面: {login_url}")

        tool.page.goto(login_url)
        # tool.page.wait_for_load_state('networkidle', timeout=3000)
        tool.page.wait_for_selector('#password, input[type="password"]', state='visible', timeout=3000)
        # time.sleep(0.5)  # 增加等待时间

        print(f"[增强登录] 登录页面加载完成")
        password_input = tool.page.locator('#password, input[type="password"]').first
        password_input.clear()
        password_input.fill(str(tool.password))
        # 使用组合操作点击登录按钮
        login_button = tool.page.locator('#a_login, button:has-text("登录"), [onclick*="login"]').first
        # with tool.page.expect_navigation(url=f"{tool.base_url}/index.html*", timeout=10000):
        login_button.click()

        # 8. 跳转到目标页面
        target_url = f"{tool.base_url}/{target_page}"
        print(f"[增强登录] 跳转到目标页面: {target_url}")

        tool.page.goto(target_url)
        tool.page.wait_for_load_state('networkidle', timeout=15000)
        # time.sleep(2)

        # 9. 验证页面加载
        final_url = tool.page.url
        title = tool.page.title()

        print(f"[增强登录] 最终页面标题: {title}")
        print(f"[增强登录] 最终URL: {final_url}")

        # 检查是否成功到达目标页面
        if target_page in final_url or title:
            print(f"[增强登录] 成功到达目标页面")
            logging.info(f"成功到达目标页面: {target_page}")
            return True
        else:
            print(f"[增强登录] 可能未成功到达目标页面，但继续执行")
            return True  # 宽松检查，避免误判

    except Exception as e:
        print(f"[增强登录] 登录流程失败: {str(e)}")
        logging.error(f"浏览器登录流程失败: {e}")
        # import traceback
        # print(f"[增强登录] 详细错误: {traceback.format_exc()}")
        return False


# -*- coding: utf-8 -*-
"""
DVR Web Automation Tool - Sync Version using sync_playwright
"""

import logging
import time
from playwright.sync_api import sync_playwright


class SyncWebTool:
    """Synchronous Web Automation Tool using sync_playwright"""

    def __init__(self, device_ip="**************", password="111111", headless=False):
        self.device_ip = device_ip
        self.base_url = f"http://{device_ip}"
        self.password = password
        self.headless = headless
        self.playwright = None
        self.browser = None
        self.page = None

        # Setup logger
        logging.basicConfig(level=logging.INFO, format='%(message)s')
        self.logger = logging.getLogger(__name__)

    def start(self):
        """Start browser with proper context management"""
        try:
            # Use context manager for better resource management
            self.playwright = sync_playwright().start()

            # Try to launch browser without hardcoded path first
            try:
                self.browser = self.playwright.chromium.launch(
                    executable_path=".\\ms-playwright\\chromium-1161\\chrome-win\\chrome.exe",
                    headless=self.headless,
                    args=['--no-sandbox', '--disable-dev-shm-usage']
                )
                self.logger.info("Browser launched with hardcoded path")

            except Exception as e1:
                self.logger.warning(f"Default chromium launch failed: {e1}")
                # Try with hardcoded path as fallback
                try:
                    self.browser = self.playwright.chromium.launch(
                        headless=self.headless,
                        args=['--no-sandbox', '--disable-dev-shm-usage']  # Better compatibility
                    )
                    self.logger.info("Browser launched with default chromium")

                except Exception as e2:
                    self.logger.error(f"Hardcoded path launch also failed: {e2}")
                    raise e2

            self.page = self.browser.new_page()
            self.page.set_default_timeout(30000)
            self.logger.info("Browser started successfully")
            return True
        except Exception as e:
            self.logger.error(f"Browser start failed: {e}")
            print(f"Browser start failed: {e}")  # 添加控制台输出
            # Clean up on failure
            self._cleanup_resources()
            return False

    def _cleanup_resources(self):
        """Clean up browser resources"""
        try:
            if self.page:
                self.page.close()
                self.page = None
        except:
            pass

        try:
            if self.browser:
                self.browser.close()
                self.browser = None
        except:
            pass

        try:
            if self.playwright:
                self.playwright.stop()
                self.playwright = None
        except:
            pass

    def close(self):
        """Close browser with proper cleanup"""
        try:
            self._cleanup_resources()
            self.logger.info("Browser closed")
        except Exception as e:
            self.logger.error(f"Error closing browser: {e}")

    def login_first(self):
        """Always login first to ensure session is valid"""
        try:
            # 1. Go to login page directly
            login_url = f"{self.base_url}/login.html"
            self.logger.info(f"Going to login page: {login_url}")

            self.page.goto(login_url)
            self.page.wait_for_load_state('networkidle')
            # time.sleep(1)

            # 2. Find and fill password
            password_input = None
            try:
                # Try multiple ways to find password input
                # password_input = self.page.locator('input[type="text"]').first
                password_input = self.page.locator('#password')
                password_input.wait_for(state='visible', timeout=3000)
            except:
                try:
                    password_input = self.page.locator('input').first
                    password_input.wait_for(state='visible', timeout=5000)
                except:
                    raise Exception("Password input field not found")

            if password_input:
                self.logger.info("Found password input, entering password...")
                password_input.clear()
                password_input.fill(self.password)
                time.sleep(0.5)

                # 3. Find and click login button
                login_button = None
                try:
                    login_button = self.page.locator('#a_login')
                    login_button.wait_for(state='visible', timeout=3000)
                except:
                    try:
                        login_button = self.page.locator('[onclick], button, input[type="submit"]').first
                        login_button.wait_for(state='visible', timeout=3000)
                    except:
                        raise Exception("Login button not found")

                if login_button:
                    self.logger.info("Found login button, clicking...")
                    login_button.click()

                    # 4. Wait for login completion
                    try:
                        self.page.wait_for_url(f"{self.base_url}/index.html*", timeout=10000)
                    except:
                        time.sleep(3)
                        current_url = self.page.url
                        if "login.html" not in current_url:
                            self.logger.info("Login may have succeeded")
                        else:
                            raise Exception("Login failed")

                    self.page.wait_for_load_state('networkidle')
                    self.logger.info("Login successful")
                    return True
                else:
                    raise Exception("Login button not found")
            else:
                raise Exception("Password input field not found")

        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False

    def goto_page(self, target_page="preview_function.html"):
        """Navigate to target page (assumes already logged in)"""
        try:
            target_url = f"{self.base_url}/{target_page}"
            self.logger.info(f"Navigating to: {target_url}")

            self.page.goto(target_url)
            self.page.wait_for_load_state('networkidle')
            time.sleep(1)

            final_url = self.page.url
            title = self.page.title()

            self.logger.info(f"Final page: {title}")
            self.logger.info(f"URL: {final_url}")
            return True

        except Exception as e:
            self.logger.error(f"Navigation failed: {e}")
            return False

    def login_and_goto(self, target_page="preview_function.html"):
        """Login first, then navigate to target page"""
        try:
            # Always login first
            if not self.login_first():
                return False

            # Then navigate to target page
            return self.goto_page(target_page)

        except Exception as e:
            self.logger.error(f"Login and navigation failed: {e}")
            return False

    def take_screenshot(self, filename=None):
        """Take screenshot"""
        try:
            if not filename:
                filename = f"screenshot_{int(time.time())}.png"

            self.page.screenshot(path=filename)
            self.logger.info(f"Screenshot saved: {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"Screenshot failed: {e}")
            return None

    def get_page_info(self):
        """Get page information"""
        try:
            return {
                'url': self.page.url,
                'title': self.page.title()
            }
        except Exception as e:
            self.logger.error(f"Get page info failed: {e}")
            return {}

    def fill_form_field(self, field_name, value, field_type="textbox"):
        """Fill form field"""
        try:
            if field_type == "textbox":
                # Try multiple ways to find the field
                field = None
                try:
                    field = self.page.get_by_role('textbox', name=field_name)
                except:
                    try:
                        field = self.page.locator(f'input[name="{field_name}"]')
                    except:
                        field = self.page.locator(f'input[placeholder*="{field_name}"]')

                field.fill(str(value))
            elif field_type == "combobox":
                field = self.page.get_by_role('combobox', name=field_name)
                field.select_option(str(value))
            elif field_type == "checkbox":
                field = self.page.get_by_role('checkbox', name=field_name)
                if value:
                    field.check()
                else:
                    field.uncheck()

            self.logger.info(f"Filled field {field_name}: {value}")
            return True
        except Exception as e:
            self.logger.error(f"Fill field failed {field_name}: {e}")
            return False

    def click_button(self, button_name):
        """Click button"""
        try:
            # Try multiple ways to find button
            button = None
            try:
                button = self.page.get_by_role('button', name=button_name)
            except:
                try:
                    button = self.page.get_by_role('link', name=button_name)
                except:
                    try:
                        button = self.page.locator(f'text="{button_name}"')
                    except:
                        button = self.page.locator(f'[value="{button_name}"]')

            button.click()
            self.page.wait_for_load_state('networkidle')
            self.logger.info(f"Clicked: {button_name}")
            return True
        except Exception as e:
            self.logger.error(f"Click button failed {button_name}: {e}")
            return False

    def get_form_values(self):
        """Get current page form values"""
        try:
            form_data = {}

            # Get all text inputs
            textboxes = self.page.query_selector_all('input[type="text"], input[type="password"], textarea')
            for textbox in textboxes:
                name = textbox.get_attribute('name') or textbox.get_attribute('placeholder') or 'unnamed'
                value = textbox.input_value()
                if value:
                    form_data[name] = value

            # Get all select boxes
            selects = self.page.query_selector_all('select')
            for select in selects:
                name = select.get_attribute('name') or 'unnamed_select'
                value = select.input_value()
                if value:
                    form_data[name] = value

            # Get all checkboxes
            checkboxes = self.page.query_selector_all('input[type="checkbox"]')
            for checkbox in checkboxes:
                name = checkbox.get_attribute('name') or 'unnamed_checkbox'
                checked = checkbox.is_checked()
                form_data[name] = checked

            return form_data
        except Exception as e:
            self.logger.error(f"Get form values failed: {e}")
            return {}


# Page mapping
PAGE_MAP = {
    'preview': 'preview_function.html',
    'system': 'system_info.html',
    'network': 'network_info.html',
    'disk': 'disk_info.html',
    'wifi': 'wifi.html',
    'terminal': 'terminal_config.html',
    'center': 'center.html',
    'dial': 'dial.html',
    'main_stream': 'main_stream.html',
    'sub_stream': 'sub_stream.html',
    'storage': 'storage.html',
    'osd': 'osd.html',
    'io': 'io.html',
    'speed': 'speed.html',
    'gsensor': 'gsensor.html',
    'voltage': 'voltage.html',
    'serial': 'serial.html',
    'ai': 'dsm_adas_ex.html',
    'position': 'position.html',
    'general': 'general.html',
    'time_record': 'time_record.html',
    'time_snap': 'TimeSnap.html',
    'user_manage': 'user_manage.html',
    'system_clock': 'system_clock.html',
    'power_manage': 'power_manage.html',
    'upgrade': 'upgrade.html',
    'parameter': 'parameter_settings.html',
    'disk_manage': 'disk_manage.html',
    'serial_test': 'serial_test.html',
    'calibration': 'preview.html',
    'log_download': 'log_Query.html',
    'qrcode': 'QRcode_info.html',
    'moon': 'moon_info.html',
    'serial_info': 'serial_info.html'
}


def open_device_page(page_name, device_ip="**************", password="111111",
                     screenshot=True, keep_open=10):
    """Simple function to open device page - 使用浏览器复用"""
    return BrowserManager.navigate_to_page(page_name, device_ip, password, screenshot, keep_open)


def open_device_page_async(page_name, device_ip="**************", password="111111",
                           screenshot=False, keep_open=10, auto_close=True):
    """异步友好的设备页面打开函数 - 增强版"""
    print(f"[异步浏览器] 开始处理页面: {page_name}, IP: {device_ip}")

    tool = SyncWebTool(device_ip, password, headless=False)

    try:
        print(f"[异步浏览器] 启动浏览器...")
        if not tool.start():
            error_msg = f"Failed to start browser for {page_name}"
            print(f"[异步浏览器] {error_msg}")
            return False

        print(f"[异步浏览器] 浏览器启动成功，准备打开页面...")

        if page_name in PAGE_MAP:
            full_page_name = PAGE_MAP[page_name]
            print(f"[异步浏览器] 页面映射: {page_name} -> {full_page_name}")
        else:
            full_page_name = page_name
            print(f"[异步浏览器] 直接使用页面名: {full_page_name}")

        # 使用增强的登录方法
        print(f"[异步浏览器] 开始增强登录流程...")
        success = enhanced_login_and_goto(tool, full_page_name)
        # success = enhanced_login_and_goto_2(tool, full_page_name)

        if success:
            print(f"[异步浏览器] 成功打开 {page_name} 页面")
            if screenshot:
                try:
                    tool.take_screenshot(f"{page_name}_page.png")
                    print(f"[异步浏览器] 截图已保存: {page_name}_page.png")
                except Exception as e:
                    print(f"[异步浏览器] 截图失败: {str(e)}")

            if keep_open > 0:
                print(f"[异步浏览器] 浏览器将保持打开 {keep_open} 秒...")
                if not auto_close:
                    print(f"[异步浏览器] 注意: 浏览器不会自动关闭，需要手动关闭")
                time.sleep(keep_open)
        else:
            print(f"[异步浏览器] 登录或页面跳转失败: {page_name}")

        return success

    except Exception as e:
        error_msg = f"Error opening {page_name} page: {str(e)}"
        print(f"[异步浏览器] {error_msg}")
        import traceback
        print(f"[异步浏览器] 详细错误信息: {traceback.format_exc()}")
        return False
    finally:
        if auto_close:
            print(f"[异步浏览器] 关闭浏览器: {page_name}")
            tool.close()
        else:
            print(f"[异步浏览器] 浏览器保持打开: {page_name} (需要手动关闭)")


# 改变登录流程
def enhanced_login_and_goto_2(tool, target_page):
    """增强的登录和页面跳转函数，专为异步环境优化"""
    try:
        # 8. 跳转到目标页面
        target_url = f"{tool.base_url}/{target_page}"
        # tool.page.goto(target_url)
        with tool.page.expect_navigation():
            tool.page.goto(target_url)
        is_login_required = False
        try:
            # 同时检测两种可能的重定向特征
            tool.page.wait_for_url(
                lambda url: "login.html?redirect=sessionunvalid" in url,
                timeout=5000,
                wait_until="domcontentloaded"
            )
            is_login_required = True
            print("[登录] 检测到需要登录")
        except:
            is_login_required = False
            print("[登录] 未检测到需要登录")
        if is_login_required:
            # tool.page.wait_for_selector('input[type="password"]', state='visible')
            print(f"[增强登录] 开始登录流程...")
            # 填入用户名和密码
            # password_input = tool.page.locator('#password, input[type="password"]').first
            # password_input.clear()
            # password_input.fill(str(tool.password))
            #
            # # 点击登录按钮
            # login_button =tool.page.locator('#a_login, button:has-text("登录"), [onclick*="login"]').first
            # # page.click('button[type="submit"]')
            # with tool.page.expect_navigation():
            #     login_button.click()
            # 等待用户名和密码输入框的可见性
            tool.page.wait_for_selector('input[type="password"]', state='visible')

            # 填入用户名和密码
            tool.page.fill('input[type="password"]', str(tool.password))

            # 点击登录按钮
            login_button = tool.page.locator('#a_login, button:has-text("登录"), [onclick*="login"]').first
            # page.click('button[type="submit"]')
            with tool.page.expect_navigation():
                login_button.click()

            # 等待登录成功后，检查是否跳转
            tool.page.wait_for_load_state('networkidle')

            # 等待登录成功后，检查是否跳转到目标页面
            # tool.page.wait_for_load_state('networkidle', timeout=15000)
        else:
            print("[已登录] 无需再进行登录。")

        # 8. 跳转到目标页面
        # target_url = f"{tool.base_url}/{target_page}"
        # print(f"[增强登录] 跳转到目标页面: {target_url}")
        # 如果登录成功，跳转到目标页面
        with tool.page.expect_navigation():
            tool.page.goto(target_url)

        # tool.page.wait_for_load_state('networkidle', timeout=15000)
        # time.sleep(2)

        # 9. 验证页面加载
        final_url = tool.page.url
        title = tool.page.title()

        print(f"[增强登录] 最终页面标题: {title}")
        print(f"[增强登录] 最终URL: {final_url}")

        # 检查是否成功到达目标页面
        if target_page in final_url or title:
            print(f"[增强登录] 成功到达目标页面")
            return True
        else:
            print(f"[增强登录] 可能未成功到达目标页面，但继续执行")
            return True  # 宽松检查，避免误判

    except Exception as e:
        print(f"[增强登录] 登录流程失败: {str(e)}")
        import traceback
        print(f"[增强登录] 详细错误: {traceback.format_exc()}")
        return False
def test():
    from playwright.sync_api import sync_playwright

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        page.goto("https://baidu.com", wait_until="networkidle")

        # 动态最小化窗口


        # 后续操作...
        time.sleep(5)
        page.evaluate('''() => {
                   window.moveTo(0, screen.height);  # 移至屏幕外
                   window.resizeTo(1, 1);            # 缩至最小尺寸
               }''')
        time.sleep(10)
        browser.close()

def test_browser_reuse():
    """测试浏览器复用功能"""
    print("=== 测试浏览器复用功能 ===")

    # 第一次打开页面
    print("\n1. 第一次打开wifi页面...")
    success1 = BrowserManager.navigate_to_page('wifi', keep_open=2)
    print(f"结果: {'成功' if success1 else '失败'}")

    # 第二次打开不同页面，应该复用浏览器
    print("\n2. 复用浏览器打开system页面...")
    success2 = BrowserManager.navigate_to_page('system', keep_open=2)
    print(f"结果: {'成功' if success2 else '失败'}")

    # 第三次打开另一个页面
    print("\n3. 复用浏览器打开preview页面...")
    success3 = BrowserManager.navigate_to_page('preview', keep_open=2)
    print(f"结果: {'成功' if success3 else '失败'}")

    # 关闭浏览器
    print("\n4. 关闭浏览器...")
    BrowserManager.close_browser()
    print("浏览器已关闭")

    print(f"\n=== 测试完成 ===")
    print(f"总体结果: {'全部成功' if all([success1, success2, success3]) else '有失败'}")


if __name__ == "__main__":
    print("Testing sync web tool with browser reuse...")

    # 可以选择测试原有功能或新的复用功能
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "reuse":
        test_browser_reuse()
    elif len(sys.argv) > 1 and sys.argv[1] == "old":
        test()  # 原有的测试函数
    else:
        # 使用复用版本的函数
        print("使用浏览器复用版本打开wifi页面...")
        open_device_page('wifi', keep_open=5)
